import { Hono } from 'hono';
import { getCookie, setCookie } from 'hono/cookie';
import {
	registerUser,
	loginUser,
	logoutUser
} from '../../server/auth/auth.service';
import { authMiddleware } from '../../server/auth/auth.middleware';
import { startGoogleOAuth, loginWithGoogle } from '../../server/auth/oauth.service';
import type { AuthResponse } from '../../server/auth/auth.types';

// In-memory store for OAuth state (replace with Redis or DB in production)
const oauthStateStore = new Map<string, { codeVerifier: string; state: string }>();

// Define custom Hono context type for OAuth state
type Variables = {
	authUserId: string;
	oauthState: { codeVerifier: string; state: string };
};

const authRouter = new Hono<{ Variables: Variables }>();



// Registration endpoint
authRouter.post('/register', async (c) => {
	const { email, password } = await c.req.json();
	if (!email || !password) {
		return c.json({ success: false, error: 'Email and password required' } as AuthResponse, 400);
	}

	try {
		const result = await registerUser(email, password);
		return c.json(result, result.success ? 201 : 400);
	} catch (error) {
		console.error('Registration route error:', error);
		return c.json({ success: false, error: 'Registration failed' } as AuthResponse, 500);
	}
});

// Login endpoint
authRouter.post('/login', async (c) => {
	const { email, password } = await c.req.json();
	if (!email || !password) {
		return c.json({ success: false, error: 'Email and password required' } as AuthResponse, 400);
	}

	try {
		console.log('email password - ', email, password);
		const result = await loginUser(email, password);
		setCookie(c, 'session', result.sessionId!);
		return c.json(result, result.success ? 200 : 401);
	} catch (error) {
		console.error('Login route error:', error);
		return c.json({ success: false, error: 'Login failed' } as AuthResponse, 500);
	}
});

// Google OAuth initiation
authRouter.get('/google', async (c) => {
	try {
		const { url, codeVerifier, state } = await startGoogleOAuth();
		oauthStateStore.set(state, { codeVerifier, state });
		console.log('Stored oauthState for state:', state);
		return c.redirect(url);
	} catch (error) {
		console.error('Google OAuth initiation error:', error);
		return c.json({ success: false, error: 'Google OAuth initiation failed' } as AuthResponse, 500);
	}
});

// Google OAuth callback
authRouter.get('/google/callback', async (c) => {
	try {
		const code = c.req.query('code');
		const state = c.req.query('state');
		console.log('Received callback with code:', code, 'state:', state);

		if (!code || !state) {
			console.error('Missing code or state in callback');
			return c.json({ success: false, error: 'Invalid state or code' } as AuthResponse, 400);
		}

		const oauthState = oauthStateStore.get(state);
		if (!oauthState || oauthState.state !== state) {
			console.error('State mismatch. Received:', state, 'Stored:', oauthState?.state);
			return c.json({ success: false, error: 'Invalid state' } as AuthResponse, 400);
		}

		const result = await loginWithGoogle(code, state, oauthState.codeVerifier);
		if (!result.success || !result.sessionId) {
			return c.json(result, 401);
		}

		oauthStateStore.delete(state); // Clean up state
		setCookie(c, 'session', result.sessionId!);
		return c.redirect(`/tracker`);
	} catch (error) {
		console.error('Google OAuth callback error:', error);
		return c.json({ success: false, error: 'Google OAuth callback failed' } as AuthResponse, 500);
	}
});

// Validate session endpoint
authRouter.get('/validate', authMiddleware, async (c) => {
	const authUserId = getCookie(c, 'session');
	return c.json({ success: true, authUserId } as AuthResponse);
});

// Logout endpoint
authRouter.post('/logout', async (c) => {
	const { sessionId } = await c.req.json();
	if (!sessionId) {
		return c.json({ success: false, error: 'Session ID required' } as AuthResponse, 400);
	}

	try {
		const result = await logoutUser(sessionId);
		return c.json(result, result.success ? 200 : 400);
	} catch (error) {
		console.error('Logout route error:', error);
		return c.json({ success: false, error: 'Logout failed' } as AuthResponse, 500);
	}
});

export { authRouter };
