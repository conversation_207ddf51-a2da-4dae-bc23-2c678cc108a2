{"name": "time-tracker", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check .", "db:start": "docker compose up", "db:push": "drizzle-kit push", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio"}, "devDependencies": {"@iconify/json": "^2.2.356", "@iconify/svelte": "^5.0.0", "@sveltejs/adapter-auto": "^6.0.0", "@sveltejs/kit": "^2.16.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@tailwindcss/vite": "^4.0.0", "@types/bun": "^1.2.18", "@types/node": "^20", "dotenv": "^17.0.1", "drizzle-kit": "^0.30.2", "prettier": "^3.4.2", "prettier-plugin-svelte": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.11", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "tailwindcss": "^4.0.0", "typescript": "^5.0.0", "unplugin-icons": "^22.1.0", "vite": "^6.2.6"}, "dependencies": {"@hono/zod-validator": "^0.7.0", "@inlang/paraglide-js": "^2.0.0", "@types/jsonwebtoken": "^9.0.10", "drizzle-orm": "^0.40.0", "hono": "^4.8.3", "iconify-icon": "^3.0.0", "jsonwebtoken": "^9.0.2", "openid-client": "^6.6.2", "postgres": "^3.4.5", "tailwind-merge": "^3.3.1", "zod": "^3.25.67"}}