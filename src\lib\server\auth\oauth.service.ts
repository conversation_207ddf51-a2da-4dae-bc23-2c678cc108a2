import * as client from 'openid-client';
import { randomUUID } from 'crypto';
import { eq, and } from 'drizzle-orm';
import { db } from '../db';
import { auth_users, auth_providers, profiles } from '../db/schema';
import type { AuthResponse } from './auth.types';
import { createSession } from './auth.service';

/**
 * Memory-efficient OAuth state store with LRU cache behavior
 *
 * Features:
 * - Bounded memory usage with configurable max size
 * - LRU eviction policy (removes least recently used entries when full)
 * - Automatic entry expiration
 * - Metrics for monitoring
 *
 * For production, replace with Redis or database implementation
 */
class OAuthStateStore {
  private store = new Map<string, {
    codeVerifier: string;
    expiresAt: number;
  }>();
  private maxSize: number;
  private expirationMs: number;
  private metrics: {
    hits: number;
    misses: number;
    expirations: number;
    evictions: number;
  } = {
    hits: 0,
    misses: 0,
    expirations: 0,
    evictions: 0
  };
  
  constructor(options: {
    maxSize?: number;
    expirationMs?: number;
  } = {}) {
    this.maxSize = options.maxSize || 1000;
    this.expirationMs = options.expirationMs || 10 * 60 * 1000; // Default: 10 minutes
    
    // Periodically clean expired entries (every minute)
    setInterval(() => this.cleanExpired(), 60 * 1000);
  }
  
  set(state: string, codeVerifier: string): void {
    // If we're at capacity, remove the oldest entry
    if (this.store.size >= this.maxSize) {
      const iterator = this.store.keys().next();
      if (!iterator.done) {
        this.store.delete(iterator.value);
        this.metrics.evictions++;
      }
    }
    
    this.store.set(state, {
      codeVerifier,
      expiresAt: Date.now() + this.expirationMs
    });
  }
  
  get(state: string): string | undefined {
    const entry = this.store.get(state);
    
    if (!entry) {
      this.metrics.misses++;
      return undefined;
    }
    
    // Check if expired
    if (Date.now() > entry.expiresAt) {
      this.delete(state);
      this.metrics.expirations++;
      this.metrics.misses++;
      return undefined;
    }
    
    this.metrics.hits++;
    return entry.codeVerifier;
  }
  
  delete(state: string): void {
    this.store.delete(state);
  }
  
  /**
   * Clean up expired entries to free memory
   */
  private cleanExpired(): void {
    const now = Date.now();
    for (const [state, entry] of this.store.entries()) {
      if (now > entry.expiresAt) {
        this.store.delete(state);
        this.metrics.expirations++;
      }
    }
  }
  
  /**
   * Get current metrics and store size
   */
  getStats(): {
    size: number;
    metrics: {
      hits: number;
      misses: number;
      expirations: number;
      evictions: number;
    };
  } {
    return {
      size: this.store.size,
      metrics: { ...this.metrics }
    };
  }
  
  /**
   * Clear all entries (useful for testing or server shutdown)
   */
  clear(): void {
    this.store.clear();
  }
}

// Create store with default settings
// For production, consider injecting this as a dependency
// to allow different implementations (Redis, DB, etc.)
const oauthStateStore = new OAuthStateStore({
  maxSize: parseInt(Bun.env.OAUTH_STATE_STORE_MAX_SIZE || '1000', 10),
  expirationMs: parseInt(Bun.env.OAUTH_STATE_STORE_EXPIRATION_MS || String(10 * 60 * 1000), 10)
});

let config: client.Configuration;

async function initializeGoogleClient() {
  if (!config) {
    const server = new URL('https://accounts.google.com');
    const clientId = Bun.env.GOOGLE_CLIENT_ID || '';
    const clientSecret = Bun.env.GOOGLE_CLIENT_SECRET || '';
    if (!clientId || !clientSecret) {
      throw new Error('Google OAuth client ID or secret not configured');
    }
    config = await client.discovery(server, clientId, clientSecret);
  }
  return config;
}

export async function startGoogleOAuth(): Promise<{
  url: string;
  codeVerifier: string;
  state: string;
}> {
  const config = await initializeGoogleClient();
  const redirectUri = 'http://localhost:5173/api/auth/google/callback';
  const scope = 'openid email profile';
  const codeVerifier = client.randomPKCECodeVerifier();
  const codeChallenge = await client.calculatePKCECodeChallenge(codeVerifier);
  const state = randomUUID();
  const codeChallengeMethod = 'S256';

  const parameters: Record<string, string> = {
    redirect_uri: redirectUri,
    scope,
    code_challenge: codeChallenge,
    code_challenge_method: codeChallengeMethod,
    state, // Always include state for consistency
  };

  const redirectTo = client.buildAuthorizationUrl(config, parameters);
  console.log('Generated OAuth state:', state, 'codeVerifier:', codeVerifier);
  oauthStateStore.set(state, codeVerifier); // Store state and verifier

  return { url: redirectTo.href, codeVerifier, state };
}

export async function loginWithGoogle(
  code: string | undefined,
  expectedState: string | undefined,
  codeVerifier?: string
): Promise<AuthResponse> {
  try {
    console.log('Handling Google OAuth callback with code:', code, 'state:', expectedState);
    const config = await initializeGoogleClient();
    const redirectUri = 'http://localhost:5173/api/auth/google/callback';

    // Retrieve stored codeVerifier
    const storedCodeVerifier = oauthStateStore.get(expectedState || '');
    console.log('Retrieved codeVerifier from store:', storedCodeVerifier);

    if (!code || !expectedState || !storedCodeVerifier) {
      console.error('Invalid code or state. Code:', code, 'Expected state:', expectedState, 'Stored codeVerifier exists:', !!storedCodeVerifier);
      return { success: false, error: 'Invalid state or code' };
    }

    // Use stored codeVerifier
    const tokens = await client.authorizationCodeGrant(config, new URL(`${redirectUri}?code=${code}&state=${expectedState}`), {
      pkceCodeVerifier: storedCodeVerifier,
      expectedState,
    });

    const userInfoResponse = await client.fetchProtectedResource(
      config,
      tokens.access_token,
      new URL('https://openidconnect.googleapis.com/v1/userinfo'),
      'GET'
    );
    const userInfo = await userInfoResponse.json();
    const { sub: googleId, email, name } = userInfo;
    console.log('Fetched Google user info:', { googleId, email, name });

    if (!email) {
      console.error('Email not provided by Google');
      return { success: false, error: 'Email not provided by Google' };
    }

    return await db.transaction(async (tx) => {
      let [authUser] = await tx
        .select({ id: auth_users.id })
        .from(auth_users)
        .where(eq(auth_users.email, email));

      if (!authUser) {
        console.log('Creating new user for email:', email);
        [authUser] = await tx.insert(auth_users).values({ email }).returning({ id: auth_users.id });
        
        // Create profile for new user
        console.log('Creating profile for new Google user:', authUser.id);
        await tx.insert(profiles).values({
          auth_user_id: authUser.id,
          display_name: name || email.split('@')[0],
          full_name: name,
          avatar_url: userInfo.picture // Google often provides a picture URL
        });
      } else {
        // Check if profile exists, create if it doesn't
        const [existingProfile] = await tx
          .select({ id: profiles.id })
          .from(profiles)
          .where(eq(profiles.auth_user_id, authUser.id));
          
        if (!existingProfile) {
          console.log('Creating missing profile for existing Google user:', authUser.id);
          await tx.insert(profiles).values({
            auth_user_id: authUser.id,
            display_name: name || email.split('@')[0],
            full_name: name,
            avatar_url: userInfo.picture
          });
        }
      }
      console.log('User ID for email:', email, 'is:', authUser.id);

      const [existingProvider] = await tx
        .select({ id: auth_providers.id })
        .from(auth_providers)
        .where(
          and(
            eq(auth_providers.auth_user_id, authUser.id),
            eq(auth_providers.provider, 'google'),
            eq(auth_providers.provider_id, googleId)
          )
        );

      if (!existingProvider) {
        console.log('Creating new Google provider for user:', authUser.id);
        await tx.insert(auth_providers).values({
          auth_user_id: authUser.id,
          provider: 'google',
          provider_id: googleId,
          provider_data: JSON.stringify({
            accessToken: tokens.access_token,
            refreshToken: tokens.refresh_token || null,
          }),
        });
      } else {
        console.log('Updating Google provider for user:', authUser.id);
        await tx
          .update(auth_providers)
          .set({
            provider_data: JSON.stringify({
              accessToken: tokens.access_token,
              refreshToken: tokens.refresh_token || null,
            }),
          })
          .where(eq(auth_providers.id, existingProvider.id));
      }

      const sessionId = await createSession(authUser.id, tx);
      console.log('Google OAuth login successful for email:', email, 'with sessionId:', sessionId);
      oauthStateStore.delete(expectedState); // Clean up state
      return { success: true, authUserId: authUser.id, sessionId };
    });
  } catch (error) {
    console.error('Google OAuth login error:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Google OAuth login failed' };
  }
}