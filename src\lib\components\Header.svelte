<script lang="ts">
	import { page } from '$app/state';

	import LogoSvg from '$lib/assets/logo.svg';

	import MynauiClockWaves from '~icons/mynaui/clock-waves';
	import MynauiBriefcase from '~icons/mynaui/briefcase';
	import MynauiPresentation from '~icons/mynaui/presentation';

	import MynauiUserSquareSolid from '~icons/mynaui/user-square-solid';
	import { twJoin, twMerge } from 'tailwind-merge';

	const navItems = [
		{ name: 'Tracker', href: '/tracker', icon: MynauiClockWaves },
		{ name: 'Manager', href: '/manager', icon: MynauiBriefcase },
		{ name: 'Reporter', href: '/reporter', icon: MynauiPresentation }
	];

	let { user = {} } = $props();
</script>

<header>
	<div class="flex items-center justify-between border-b border-stone-300 py-4">
		<img src={LogoSvg} alt="Logo" class="h-9 object-contain" />
		<div class="flex w-full items-center gap-6">
			<nav aria-label="Main menu" class="mr-auto ml-16">
				<ul class="flex gap-2">
					{#each navItems as item (item.name)}
						<li>
							<a
								href={item.href}
								class={twMerge(
									'group relative inline-flex items-center gap-2 overflow-hidden rounded-full bg-white/30 p-2 px-4 text-slate-700 transition-colors duration-500',
									item.href === page.url.pathname
										? 'bg-primary text-white'
										: 'hover:bg-white hover:text-slate-900'
								)}
							>
								<item.icon class="size-6"></item.icon>
								<div class="inline-block group-hover:-translate-y-[calc(100%+1ch)] transition-transform duration-400">
									<div class="text-sm font-medium uppercase">{item.name}</div>
									<div class="absolute top-[calc(100%+1ch)] text-sm font-medium uppercase">{item.name}</div>
								</div>
							</a>
						</li>
					{/each}
				</ul>
			</nav>

			<!-- Profile menu -->
			<div class="relative">
				<button
					class="hover:text-primary flex items-center gap-1 rounded-full px-4 py-2 text-slate-700 transition-colors"
				>
					<MynauiUserSquareSolid class="size-8" />
					<span class="font-medium">
						{user.name ? user.name : 'Profile'}
					</span>
				</button>
			</div>
		</div>
	</div>
</header>
