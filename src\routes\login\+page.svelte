<script lang="ts">
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  import { browser } from '$app/environment';

  let email = '';
  let password = '';
  let error: string | null = null;
  let loading = false;

  $: if (browser && $page.url.searchParams.get('sessionId')) {
    const sessionId = $page.url.searchParams.get('sessionId');
    if (sessionId) {
      localStorage.setItem('sessionId', sessionId);
      goto('/');
    }
  }

  async function login() {
    if (loading) return;
    loading = true;
    error = null;
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email, password }),
      });
      const result = await response.json();
      if (!result.success) {
        error = result.error;
        return;
      }
      if (result.sessionId) {
        localStorage.setItem('sessionId', result.sessionId);
        goto('/');
      }
    } catch (err) {
      error = 'Login failed';
    } finally {
      loading = false;
    }
  }

  async function googleLogin() {
    if (loading) return;
    loading = true;
    error = null;
    try {
      window.location.href = '/api/auth/google';
    } catch (err) {
      error = 'Google login initiation failed';
      loading = false;
    }
  }
</script>

<div class="container">
  <h1>Login</h1>
  {#if error}
    <p class="error">{error}</p>
  {/if}
  <form on:submit|preventDefault={login}>
    <div class="form-group">
      <label for="email">Email</label>
      <input id="email" type="email" bind:value={email} placeholder="Enter email" required disabled={loading} />
    </div>
    <div class="form-group">
      <label for="password">Password</label>
      <input id="password" type="password" bind:value={password} placeholder="Enter password" required disabled={loading} />
    </div>
    <button type="submit" disabled={loading}>{loading ? 'Logging in...' : 'Login'}</button>
  </form>
  <button class="google-btn" on:click={googleLogin} disabled={loading}>
    {loading ? 'Processing...' : 'Sign in with Google'}
  </button>
</div>

<style>
  .container {
    max-width: 400px;
    margin: 2rem auto;
    padding: 1rem;
  }
  .form-group {
    margin-bottom: 1rem;
  }
  label {
    display: block;
    margin-bottom: 0.5rem;
  }
  input {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ccc;
    border-radius: 4px;
  }
  button {
    width: 100%;
    padding: 0.75rem;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
  }
  button:disabled {
    background-color: #6c757d;
    cursor: not-allowed;
  }
  .google-btn {
    background-color: #4285f4;
    margin-top: 1rem;
  }
  .error {
    color: red;
    margin-bottom: 1rem;
  }
</style>