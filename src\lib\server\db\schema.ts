import {
	pgTable,
	uuid,
	varchar,
	timestamp,
	jsonb,
	index,
	unique,
	text,
	integer
} from 'drizzle-orm/pg-core';
import { relations, sql } from 'drizzle-orm';
import { pgEnum } from 'drizzle-orm/pg-core';

// Define provider enum for auth_providers
export const providerEnum = pgEnum('provider', ['email', 'google']);

// Authentication Users table (core user identity)
export const auth_users = pgTable(
	'auth_users',
	{
		id: uuid('id')
			.primaryKey()
			.default(sql`gen_random_uuid()`),
		email: varchar('email', { length: 255 }).unique().notNull(),
		created_at: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
		updated_at: timestamp('updated_at', { withTimezone: true }).defaultNow()
	},
	(table) => [index('idx_auth_users_email').on(table.email)]
);

// Authentication Providers table (supports email and Google OAuth)
export const auth_providers = pgTable(
	'auth_providers',
	{
		id: uuid('id')
			.primaryKey()
			.default(sql`gen_random_uuid()`),
		auth_user_id: uuid('auth_user_id')
			.references(() => auth_users.id, { onDelete: 'cascade' })
			.notNull(),
		provider: providerEnum('provider').notNull(),
		provider_id: varchar('provider_id', { length: 255 }).notNull(),
		provider_data: jsonb('provider_data').notNull(),
		created_at: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
		updated_at: timestamp('updated_at', { withTimezone: true }).defaultNow()
	},
	(table) => [
		unique('unique_provider_per_user').on(table.auth_user_id, table.provider),
		index('idx_auth_providers_auth_user_id').on(table.auth_user_id),
		index('idx_auth_providers_provider_id').on(table.provider_id)
	]
);

// Sessions table (for managing user sessions)
export const sessions = pgTable(
	'sessions',
	{
		id: uuid('id')
			.primaryKey()
			.default(sql`gen_random_uuid()`),
		auth_user_id: uuid('auth_user_id')
			.references(() => auth_users.id, { onDelete: 'cascade' })
			.notNull(),
		token: varchar('token', { length: 512 }).notNull().unique(),
		expires_at: timestamp('expires_at', { withTimezone: true }).notNull(),
		created_at: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
		updated_at: timestamp('updated_at', { withTimezone: true }).defaultNow()
	},
	(table) => [
		index('idx_sessions_auth_user_id').on(table.auth_user_id),
		index('idx_sessions_token').on(table.token)
	]
);

// Profiles table (user-specific data)
export const profiles = pgTable(
	'profiles',
	{
		id: uuid('id')
			.primaryKey()
			.default(sql`gen_random_uuid()`),
		auth_user_id: uuid('auth_user_id')
			.references(() => auth_users.id, { onDelete: 'cascade' })
			.notNull()
			.unique(),
		display_name: varchar('display_name', { length: 255 }),
		full_name: varchar('full_name', { length: 255 }),
		avatar_url: varchar('avatar_url', { length: 1024 }),
		bio: text('bio'),
		preferences: jsonb('preferences').default('{}'),
		locale: varchar('locale', { length: 10 }).default('en'),
		created_at: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
		updated_at: timestamp('updated_at', { withTimezone: true }).defaultNow()
	},
	(table) => [index('idx_profiles_auth_user_id').on(table.auth_user_id)]
);

// Time Entities table (for tracking time entries)
export const time_entries = pgTable(
	'time_entries',
	{
		id: uuid('id')
			.primaryKey()
			.default(sql`gen_random_uuid()`),
		auth_user_id: uuid('auth_user_id')
			.references(() => auth_users.id, { onDelete: 'cascade' })
			.notNull(),
		start_time: timestamp('start_time', { withTimezone: true }).notNull(),
		end_time: timestamp('end_time', { withTimezone: true }),
		duration_seconds: integer('duration_seconds').generatedAlwaysAs(sql`
      CASE WHEN end_time IS NULL THEN NULL 
      ELSE EXTRACT(EPOCH FROM (end_time - start_time))::INTEGER END
    `),
		description: text('description'),
		tags: jsonb('tags').default('[]'),
		created_at: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
		updated_at: timestamp('updated_at', { withTimezone: true }).defaultNow()
	},
	(table) => [index('idx_time_entries_auth_user_id').on(table.auth_user_id)]
);

export const active_timers = pgTable('active_timers', {
	id: uuid('id')
		.primaryKey()
		.default(sql`gen_random_uuid()`),
	auth_user_id: uuid('auth_user_id')
		.references(() => auth_users.id, { onDelete: 'cascade' })
		.notNull()
		.unique(),
	time_entry_id: uuid('time_entry_id')
		.references(() => time_entries.id, { onDelete: 'cascade' })
		.notNull()
		.unique(),
	created_at: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
	updated_at: timestamp('updated_at', { withTimezone: true }).defaultNow()
})

export const projects = pgTable('projects', {
	id: uuid('id')
		.primaryKey()
		.default(sql`gen_random_uuid()`),
	auth_user_id: uuid('auth_user_id')
		.references(() => auth_users.id, { onDelete: 'cascade' })
		.notNull()
		.unique(),
	name: varchar('name', { length: 255 }).notNull(),
	description: text('description'),
	created_at: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
	updated_at: timestamp('updated_at', { withTimezone: true }).defaultNow()
});

export const projects_notes = pgTable('projects_notes', {
	id: uuid('id')
		.primaryKey()
		.default(sql`gen_random_uuid()`),
	project_id: uuid('project_id')
		.references(() => projects.id, { onDelete: 'cascade' })
		.notNull()
		.unique(),
	content: text('content').notNull(),
	created_at: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
	updated_at: timestamp('updated_at', { withTimezone: true }).defaultNow()
});

// Relations
export const authUsersRelations = relations(auth_users, ({ many, one }) => ({
	auth_providers: many(auth_providers),
	sessions: many(sessions),
	time_entries: many(time_entries),
	active_timers: many(active_timers),
	projects: many(projects),
	profile: one(profiles, {
		fields: [auth_users.id],
		references: [profiles.auth_user_id]
	}),
}));

export const authProvidersRelations = relations(auth_providers, ({ one }) => ({
	auth_user: one(auth_users, {
		fields: [auth_providers.auth_user_id],
		references: [auth_users.id]
	})
}));

export const sessionsRelations = relations(sessions, ({ one }) => ({
	auth_user: one(auth_users, {
		fields: [sessions.auth_user_id],
		references: [auth_users.id]
	})
}));

export const profilesRelations = relations(profiles, ({ one }) => ({
	auth_user: one(auth_users, {
		fields: [profiles.auth_user_id],
		references: [auth_users.id]
	})
}));

export const timeEntriesRelations = relations(time_entries, ({ one }) => ({
	auth_user: one(auth_users, {
		fields: [time_entries.auth_user_id],
		references: [auth_users.id]
	}),
	project: one(projects, {
		fields: [time_entries.auth_user_id],
		references: [projects.auth_user_id],
	}),
	active_timer: one(active_timers, {
		fields: [time_entries.id],
		references: [active_timers.time_entry_id]
	})

}));

export const projectsRelations = relations(projects, ({ one, many }) => ({
	auth_user: one(auth_users, {
		fields: [projects.auth_user_id],
		references: [auth_users.id]
	}),
	time_entries: many(time_entries)
}));

export const projectsNotesRelations = relations(projects_notes, ({ one }) => ({
	project: one(projects, {
		fields: [projects_notes.project_id],
		references: [projects.id]
	})
}));

export const activeTimersRelations = relations(active_timers, ({ one }) => ({
	auth_user: one(auth_users, {
		fields: [active_timers.auth_user_id],
		references: [auth_users.id]
	}),
	time_entry: one(time_entries, {
		fields: [active_timers.time_entry_id],
		references: [time_entries.id]
	})
}));
