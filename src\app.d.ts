// See https://svelte.dev/docs/kit/types#app.d.ts
// for information about these interfaces
declare global {
	namespace App {
		// interface Error {}
		interface Locals {
			user: any
			// {
			// 	id: string;
			// 	email: string;
			// 	name?: string;
			// 	profile?: {
			// 		id: string;
			// 		auth_user_id: string;
			// 		display_name?: string;
			// 		full_name?: string;
			// 		avatar_url?: string;
			// 		bio?: string;
			// 		preferences?: Record<string, any>;
			// 		locale?: string;
			// 		created_at: Date;
			// 		updated_at?: Date;
			// 	} | null;
			// } | null;
		}
		// interface PageData {}
		// interface PageState {}
		// interface Platform {}
	}
}

export {};
