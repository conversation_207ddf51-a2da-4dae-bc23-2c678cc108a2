import { sequence } from '@sveltejs/kit/hooks';
import { redirect, type Handle } from '@sveltejs/kit';
import { paraglideMiddleware } from '$lib/paraglide/server';
import { validateSession } from '$lib/server/auth/auth.service';

const handleAuth: Handle = async ({ event, resolve }) => {
	const session = event.cookies.get('session');
	if (session) {
		event.locals.user = await getUserFromSession(session);
	}

	if (event.url.pathname.startsWith('/login')) {
		return redirect(302, '/tracker');
	}
	return await resolve(event);
};

const handleParaglide: Handle = async ({ event, resolve }) =>
	await paraglideMiddleware(event.request, ({ request, locale }) => {
		event.request = request;
		return resolve(event, {
			transformPageChunk: ({ html }) => html.replace('%paraglide.lang%', locale)
		});
	});

export const handle: Handle = sequence(handleAuth, handleParaglide);

async function getUserFromSession(session: string | undefined) {
	if (!session) {
		return null;
	}

	const result = await validateSession(session);
	return result.success && result.data ? result.data : null;
}
