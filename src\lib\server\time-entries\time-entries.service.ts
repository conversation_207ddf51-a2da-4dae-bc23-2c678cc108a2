import { eq, and, sql, count, asc, desc, gt, lt, gte, lte, between } from 'drizzle-orm';
import { db } from '../db';
import { active_timers, time_entries } from '../db/schema';

export async function createTimeEntry(
	authUserId: string,
	startTime: Date,
	endTime?: Date,
	description?: string,
	tags: string[] = []
): Promise<any> {
	const [timeEntry] = await db
		.insert(time_entries)
		.values({
			auth_user_id: authUserId,
			start_time: startTime,
			end_time: endTime,
			description,
			tags
		})
		.returning();

	return timeEntry;
}

export async function createActiveTimer(authUserId: string, timeEntryId: string): Promise<any> {
	const [activeTimer] = await db
		.insert(active_timers)
		.values({
			auth_user_id: authUserId,
			time_entry_id: timeEntryId
		})
		.returning();

	return activeTimer;
}

export async function stopUserTimer(authUserId: string): Promise<any> {
	return await db.transaction(async (tx) => {
		const [timeEntry] = await tx
			.update(time_entries)
			.set({ end_time: new Date() })
			.where(
				and(
					eq(time_entries.auth_user_id, authUserId),
					eq(time_entries.id, active_timers.time_entry_id)
				)
			)
			.returning();

		await tx.delete(active_timers).where(eq(active_timers.auth_user_id, authUserId));

		return timeEntry;
	});
}

// Add pagination, sorting, filtering
export async function getAllTimeEntriesForUser(
	authUserId: string,
	page = 1,
	pageSize = 10,
	sort = 'start_time:desc',
	filters: { field: string; operator: string; value: any }[] = []
): Promise<{ entries: any[]; total: number }> {
	const whereConditions = [eq(time_entries.auth_user_id, authUserId)];

	// Map operators to Drizzle functions
	const operatorMap = {
		eq: eq,
		gt: gt,
		lt: lt,
		gte: gte,
		lte: lte
	};

	// Apply filters
	for (const filter of filters) {
		const { field, operator, value } = filter;
		if (operator === 'between') {
			// @ts-expect-error
			whereConditions.push(between(time_entries[field], value[0], value[1]));
		} else {
			// @ts-expect-error
			const drizzleFunc = operatorMap[operator];
			if (drizzleFunc) {
				// @ts-expect-error
				whereConditions.push(drizzleFunc(time_entries[field], value));
			} else {
				throw new Error(`Invalid operator: ${operator}`);
			}
		}
	}

	// Total count
	const totalQuery = db
		.select({ count: count() })
		.from(time_entries)
		.where(and(...whereConditions));
	const totalResult = await totalQuery;
	const total = totalResult[0].count;

	// Build and execute the query for entries
	let entriesQuery = db
		.select()
		.from(time_entries)
		.where(and(...whereConditions));

	// Sorting
	const [sortField, sortDirection] = sort.split(':');
	if (sortDirection.toLowerCase() === 'asc') {
		// @ts-expect-error
		entriesQuery = entriesQuery.orderBy(asc(time_entries[sortField]));
	} else {
		// @ts-expect-error
		entriesQuery = entriesQuery.orderBy(desc(time_entries[sortField]));
	}

	// Pagination
	const offset = (page - 1) * pageSize;
	// @ts-expect-error
	entriesQuery = entriesQuery.limit(pageSize).offset(offset);

	const entries = await entriesQuery;
	return { entries, total };
}

export async function getTimeEntryById(timeEntryId: string): Promise<any> {
	return await db.select().from(time_entries).where(eq(time_entries.id, timeEntryId));
}
