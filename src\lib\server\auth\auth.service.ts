import { eq, and, sql } from 'drizzle-orm';
import { randomUUID } from 'crypto';
import { db } from '../db';
import { auth_users, auth_providers, sessions, profiles } from '../db/schema';
import type { AuthResponse } from './auth.types';

const SESSION_DURATION = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds

export async function createSession(authUserId: string, tx: any): Promise<string> {
	const sessionId = randomUUID();
	const expiresAt = new Date(Date.now() + SESSION_DURATION);
	console.log('Creating session for auth_user_id:', authUserId, 'with sessionId:', sessionId);
	await tx.insert(sessions).values({
		auth_user_id: authUserId,
		token: sessionId,
		expires_at: expiresAt
	});
	return sessionId;
}

export async function registerUser(
	email: string,
	password: string,
	name?: string
): Promise<AuthResponse> {
	try {
		return await db.transaction(async (tx) => {
			console.log('Starting registration transaction for email:', email);

			// Check for existing user
			const existingUser = await tx
				.select({ id: auth_users.id })
				.from(auth_users)
				.where(eq(auth_users.email, email));
			if (existingUser.length > 0) {
				console.log('Email already exists:', email);
				throw new Error('Email already registered');
			}

			// Insert user
			console.log('Inserting new user with email:', email);
			const [authUser] = await tx
				.insert(auth_users)
				.values({ email })
				.returning({ id: auth_users.id });

			if (!authUser?.id) {
				console.error('Failed to insert auth_user for email:', email);
				throw new Error('Failed to create auth user');
			}
			console.log('Inserted auth_user with id:', authUser.id);
			
			// Create profile for the user
			console.log('Creating profile for user:', authUser.id);
			await tx.insert(profiles).values({
				auth_user_id: authUser.id,
				display_name: name || email.split('@')[0], // Default to username from email
			});

			// Insert provider data
			const hashedPassword = await Bun.password.hash(password, 'bcrypt');
			console.log('Inserting auth_providers for email:', email);
			await tx
				.insert(auth_providers)
				.values({
					auth_user_id: authUser.id,
					provider: 'email',
					provider_id: email,
					provider_data: JSON.stringify({ hashedPassword })
				})
				.catch((error) => {
					console.error('Error inserting auth_providers:', error);
					throw error;
				});

			// Create session within transaction
			console.log('Creating session for auth_user_id:', authUser.id);
			const sessionId = await createSession(authUser.id, tx);
			console.log('Registration successful for email:', email, 'with sessionId:', sessionId);
			return { success: true, authUserId: authUser.id, sessionId };
		});
	} catch (error) {
		console.error('Registration error for email:', email, error);
		return {
			success: false,
			error: error instanceof Error ? error.message : 'Registration failed'
		};
	}
}

export async function loginUser(email: string, password: string): Promise<AuthResponse> {
	try {
		console.log('Starting login for email:', email);
		return await db.transaction(async (tx) => {
			// Find user by email
			const [authUser] = await tx
				.select({ id: auth_users.id })
				.from(auth_users)
				.where(eq(auth_users.email, email));

			if (!authUser) {
				console.log('User not found for email:', email);
				throw new Error('User not found');
			}
			console.log('Found auth_user with id:', authUser.id);

			// Find email provider
			const [provider] = await tx
				.select({ provider_data: auth_providers.provider_data })
				.from(auth_providers)
				.where(
					and(
						eq(auth_providers.auth_user_id, authUser.id),
						eq(auth_providers.provider, 'email'),
						eq(auth_providers.provider_id, email)
					)
				);

			if (!provider) {
				console.log('No email provider found for user:', authUser.id);
				throw new Error('Email-password authentication not set up');
			}

			// Verify password
			const rawProviderData = provider.provider_data as string;
			const providerData = JSON.parse(rawProviderData);
			const isPasswordValid = await Bun.password.verify(
				password,
				providerData.hashedPassword,
				'bcrypt'
			);
			if (!isPasswordValid) {
				console.log('Invalid password for email:', email);
				throw new Error('Invalid password');
			}

			// Create session
			console.log('Creating session for auth_user_id:', authUser.id);
			const sessionId = await createSession(authUser.id, tx);
			console.log('Login successful for email:', email, 'with sessionId:', sessionId);
			return { success: true, authUserId: authUser.id, sessionId };
		});
	} catch (error) {
		console.error('Login error for email:', email, error);
		return { success: false, error: error instanceof Error ? error.message : 'Login failed' };
	}
}

export async function validateSession(sessionId: string): Promise<AuthResponse> {
	try {
		// console.log('Validating session:', sessionId);
		return await db.transaction(async (tx) => {
			const [session] = await tx
				.select({ auth_user_id: sessions.auth_user_id, expires_at: sessions.expires_at })
				.from(sessions)
				.where(eq(sessions.token, sessionId));

			if (!session) {
				console.log('Session not found:', sessionId);
				throw new Error('Session not found');
			}

			if (session.expires_at < new Date()) {
				console.log('Session expired:', sessionId);
				await tx.delete(sessions).where(eq(sessions.token, sessionId));
				throw new Error('Session expired');
			}

			const newExpiresAt = new Date(Date.now() + SESSION_DURATION);
			await tx
				.update(sessions)
				.set({ expires_at: newExpiresAt })
				.where(eq(sessions.token, sessionId));

			const [userData] = await tx
				.select({
					email: auth_users.email,
					profile: profiles
				})
				.from(auth_users)
				.leftJoin(profiles, eq(auth_users.id, profiles.auth_user_id))
				.where(eq(auth_users.id, session.auth_user_id));

			return {
				success: true,
				data: {
					id: session.auth_user_id,
					email: userData.email,
					name: userData.profile?.display_name || userData.email.split('@')[0],
					profile: userData.profile || null
				}
			};
		});
	} catch (error) {
		console.error('Session validation error for sessionId:', sessionId, error);
		return { success: false, error: error instanceof Error ? error.message : 'Invalid session' };
	}
}

export async function logoutUser(sessionId: string): Promise<AuthResponse> {
	try {
		console.log('Logging out session:', sessionId);
		return await db.transaction(async (tx) => {
			const [session] = await tx
				.select({ id: sessions.id })
				.from(sessions)
				.where(eq(sessions.token, sessionId));

			if (!session) {
				console.log('Session not found for logout:', sessionId);
				throw new Error('Session not found');
			}

			await tx.delete(sessions).where(eq(sessions.token, sessionId));
			console.log('Logout successful for session:', sessionId);
			return { success: true };
		});
	} catch (error) {
		console.error('Logout error for sessionId:', sessionId, error);
		return { success: false, error: error instanceof Error ? error.message : 'Logout failed' };
	}
}

export async function cleanupExpiredSessions(): Promise<void> {
	try {
		console.log('Cleaning up expired sessions');
		await db.delete(sessions).where(sql`${sessions.expires_at} < NOW()`);
		console.log('Expired sessions cleaned up');
	} catch (error) {
		console.error('Cleanup error:', error);
	}
}
