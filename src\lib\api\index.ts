import { Hono } from 'hono';
import { tasksRouter } from './tasks';
import { authRouter } from './auth';
import { trackerRouter } from './tracker';

export * from './tasks';
export * from './auth';
export * from './tracker';

const app = new Hono()
  .route('/tasks', tasksRouter)
  .route('/auth', authRouter)
  .route('/tracker', trackerRouter);

export const api = new Hono().route('/api', app);

export type Router = typeof app;