<script lang="ts">
	import Header from '$lib/components/Header.svelte';

	import { makeClient } from '$lib/make-client.js';
	import { onMount } from 'svelte';

	const client = makeClient(fetch);

	let fetchPromise = $state<Promise<any>>(Promise.resolve({}));
	let currentController: AbortController | null = null;
	let timeEntries = $state<any[]>([]);
	let activeTimer = $state<any>(null);

	async function getTimeEntries() {
		// Cancel previous request if it's still pending
		if (currentController) {
			currentController.abort();
		}

		// Create new abort controller for this request
		currentController = new AbortController();

		// Set the promise immediately so the UI shows loading state
		fetchPromise = (async () => {
			try {
				const timeEntriesData = await client.tracker.$get(
					{},
					{
						init: {
							signal: currentController.signal
						}
					}
				);

				if (!timeEntriesData.ok) {
					throw new Error('Failed to fetch time entries');
				}

				timeEntries = [await timeEntriesData.json()];

				console.log(timeEntries);

				return timeEntries;
			} catch (error) {
				// Don't throw if the request was aborted
				if (error instanceof Error && error.name === 'AbortError') {
					console.log('Request was cancelled');
					return null; // Return null for cancelled requests
				}
				throw error;
			}
		})();

		return fetchPromise;
	}

	async function createTimeEntry() {
		const timeEntriesData = await client.tracker.$get();
		return timeEntries.push(await timeEntriesData.json());
	}

	async function startTimer() {
		const activeTimerData = await client.tracker.start.$get();
		return (activeTimer = await activeTimerData.json());
	}

	async function stopTimer() {
		const endedTimeEntry = await client.tracker.stop.$get();
		timeEntries.push(await endedTimeEntry.json());
		return (activeTimer = null);
	}

	onMount(async () => {
		await getTimeEntries();
	});
</script>

<div class="container">
	<Header user={{ name: 'Michael' }} />

	<div class="flex gap-4">
		{#if activeTimer}
		<pre>{JSON.stringify(activeTimer, null, 2)}</pre>
		{/if}
		<div class="">
			{#if activeTimer}
				<button class="bg-red-200 p-4" onclick={stopTimer}>Stop Timer</button>
			{:else}
				<button class="bg-green-200 p-4" onclick={startTimer}>Start Timer</button>
			{/if}
		</div>
	</div>

	<button class="bg-green-200 p-4" onclick={createTimeEntry}>createTimeEntry</button>
	<button class="bg-blue-200 p-4" onclick={getTimeEntries}>Refresh Time Entries</button>

	{#await fetchPromise}
		<p>Loading...</p>
	{:then data}
		{#if data !== null}
			<pre>{JSON.stringify(timeEntries, null, 2)}</pre>
		{:else}
			<p>Request was cancelled</p>
		{/if}
	{:catch error}
		<p>Error: {error.message}</p>
	{/await}
</div>

<pre>
    <!-- {JSON.stringify(data, null, 2)} -->
</pre>
